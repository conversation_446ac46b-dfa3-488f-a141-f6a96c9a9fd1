<?php
defined('_JEXEC') or die;

$class = '';
if ($item->anchor_css) {
    $class = 'class="' . $item->anchor_css . '"';
}
$title = $item->anchor_title ? 'title="' . $item->anchor_title . '"' : '';
$linktype = $item->title;

// Add SVG icons for adventure menu items
$adventure_icons = [
    'Best Sellers' => '/templates/zenbase/icons/adv-bestsellers.svg',
    'Bucket List Adventures' => '/templates/zenbase/icons/cat-bucket-list.svg',
    'Bucket List' => '/templates/zenbase/icons/cat-bucket-list.svg',
    'Short Trips' => '/templates/zenbase/icons/cat-beginner.svg',
    'Epic Peaks' => '/templates/zenbase/icons/adv-peaks.svg',
    'Remote Adventures' => '/templates/zenbase/icons/adv-remote.svg',
    'Training Weekends' => '/templates/zenbase/icons/adv-training.svg',
    'Hardcore Challenges' => '/templates/zenbase/icons/cat-hardcore-challenges.svg',
    'African Summits' => '/templates/zenbase/icons/adv-africa.svg'
];

// Add down arrows for specific menu items
$items_with_arrows = ['Destinations', 'Collections', 'Why Evertrek?'];
$chevron_down = '';
if (in_array($item->title, $items_with_arrows)) {
    $chevron_path = JPATH_ROOT . '/templates/zenbase/icons/chevron-down.svg';
    if (file_exists($chevron_path)) {
        $chevron_down = '<span class="zen-menu__arrow">' . file_get_contents($chevron_path) . '</span>';
    }
}

// Replace Search text with search icon
if ($item->title === 'Search') {
    $search_path = JPATH_ROOT . '/templates/zenbase/icons/search-small.svg';
    if (file_exists($search_path)) {
        $linktype = '<span class="zen-menu__icon">' . file_get_contents($search_path) . '</span>';
    }
} else if (isset($adventure_icons[$item->title])) {
    $linktype = '<img src="' . $adventure_icons[$item->title] . '" alt="" class="zen-menu__icon" />' . $linktype;
} else if ($item->menu_image) {
    if ($item->menu_image_css) {
        $image_attributes['class'] = $item->menu_image_css;
        $linktype = '<img class="' . $item->menu_image_css . '" src="' . $item->menu_image . '" alt="' . $item->title . '" />';
    } else {
        $linktype = '<img src="' . $item->menu_image . '" alt="' . $item->title . '" />';
    }
    if ($item->params->get('menu_text', 1)) {
        $linktype .= '<span class="image-title">' . $item->title . '</span>';
    }
}

// Special handling for About Us menu items
$isAboutUsMenu = false;
$parentItem = null;
if (isset($currentItem) && $currentItem->id === 362) {
    $isAboutUsMenu = true;
    $parentItem = $currentItem;
} elseif (isset($item->parent_id) && $item->parent_id === 362) {
    $isAboutUsMenu = true;
    $parentItem = $item;
}

// Ensure About Us menu items have valid links
if ($isAboutUsMenu) {
    // Special handling for About Us menu items
    if ($item->type === 'url') {
        // For URL type menu items, use the link directly
        // Make sure it's a full URL or starts with a slash
        if (strpos($item->link, 'http') === 0 || strpos($item->link, '/') === 0) {
            $item->flink = $item->link;
        } else {
            // Add a leading slash if missing
            $item->flink = '/' . $item->link;
        }
    } elseif ($item->type === 'component') {
        // Special handling for SP Page Builder links
        if (strpos($item->link, 'com_sppagebuilder') !== false) {
            // Parse the query parameters
            parse_str(parse_url($item->link, PHP_URL_QUERY), $query);
            if (isset($query['id'])) {
                // Use the standard Joomla routing for SP Page Builder pages
                $item->flink = \Joomla\CMS\Router\Route::_('index.php?option=com_sppagebuilder&view=page&id=' . $query['id']);

                // Special case for specific menu items based on database structure
                $urlMap = [
                    'Refer and Earn - EverTrekker Rewards' => '/evertrekker-rewards',
                    'Meet the Team' => '/about-us/meet-the-team',
                    'EverTrek Reviews' => '/about-us/evertrek-reviews',
                    'Reasons to choose EverTrek' => '/about-us/reasons-to-choose-evertrek',
                    'Why Choose Evertrek' => '/about-us/why-choose-evertrek',
                    'Awesomeness Guarantee' => '/about-us/awesomeness-guarantee',
                    'EverTrek Partnerships' => '/about-us/evertrek-partnerships',
                    'Charity Projects' => '/about-us/charity-projects',
                    'Responsible Travel' => '/about-us/responsible-travel',
                    'Terms & Conditions' => '/about-us/terms-conditions',
                    'Privacy Policy' => '/about-us/privacy-policy',
                    'YETI-AI Ask us Anything' => '/about-us/ask-us-anything-with-yet-ai-bot',
                    'Ask us anything with YET-AI' => '/about-us/ask-us-anything-with-yet-ai',
                    'Download our Adventure Brochure' => '/about-us/download-our-adventure-brochure'
                ];

                if (isset($urlMap[$item->title])) {
                    $item->flink = $urlMap[$item->title];
                }
            } else {
                $item->flink = $item->link;
            }
        } else {
            // For other component links
            $item->flink = $item->link;
        }
    } elseif ($item->type === 'alias') {
        // For alias type menu items, get the target menu item
        $aliasParams = new \Joomla\Registry\Registry($item->params);
        $targetId = $aliasParams->get('aliasoptions');
        if ($targetId) {
            // Use the standard Joomla routing for alias menu items
            $item->flink = \Joomla\CMS\Router\Route::_('index.php?Itemid=' . $targetId);

            // Special handling for specific alias items
            if ($item->title === 'Blog') {
                $item->flink = '/blog';
            } else if ($item->title === 'Podcast') {
                $item->flink = '/podcast';
            } else if ($item->title === 'About Us') {
                $item->flink = '/about-us';
            }
        } else {
            // If no target, use the parent menu item
            $item->flink = '/about-us';
        }
    } else {
        // Fallback to the About Us page if no valid link
        $item->flink = '/about-us';
    }

    // Special case for "Refer and Earn - EverTrekker Rewards"
    if ($item->title === 'Refer and Earn - EverTrekker Rewards') {
        $item->flink = '/evertrekker-rewards';
    }

    // Debug output
    echo "<!-- About Us menu item link debug:\n";
    echo "Title: " . $item->title . "\n";
    echo "Type: " . $item->type . "\n";
    echo "Raw link: " . $item->link . "\n";
    echo "Final flink: " . $item->flink . "\n";
    echo "-->\n";
}

// Special handling for About Us menu items to ensure they have valid URLs
if ($isAboutUsMenu && empty($item->flink)) {
    // If the link is still empty, try to generate a valid URL based on the title
    $urlMap = [
        'Refer and Earn - EverTrekker Rewards' => '/evertrekker-rewards',
        'Meet the Team' => '/about-us/meet-the-team',
        'EverTrek Reviews' => '/about-us/evertrek-reviews',
        'Reasons to choose EverTrek' => '/about-us/reasons-to-choose-evertrek',
        'Why Choose Evertrek' => '/about-us/why-choose-evertrek',
        'Awesomeness Guarantee' => '/about-us/awesomeness-guarantee',
        'EverTrek Partnerships' => '/about-us/evertrek-partnerships',
        'Charity Projects' => '/about-us/charity-projects',
        'Responsible Travel' => '/about-us/responsible-travel',
        'Terms & Conditions' => '/about-us/terms-conditions',
        'Privacy Policy' => '/about-us/privacy-policy',
        'YETI-AI Ask us Anything' => '/about-us/ask-us-anything-with-yet-ai-bot',
        'Ask us anything with YET-AI' => '/about-us/ask-us-anything-with-yet-ai',
        'Download our Adventure Brochure' => '/about-us/download-our-adventure-brochure'
    ];

    if (isset($urlMap[$item->title])) {
        $item->flink = $urlMap[$item->title];
    } else {
        // Generate a URL from the title as a fallback
        // Convert the title to a URL-safe string
        $safeTitle = strtolower(str_replace(' ', '-', preg_replace('/[^a-zA-Z0-9\s]/', '', $item->title)));
        $item->flink = '/about-us/' . $safeTitle;
    }
}

// Force URLs for About Us menu items
$href = $item->flink;
$isAboutUsMenuItem = false;

// Special handling for Collections and Destinations top-level items
if ($item->title === 'Collections' || $item->title === 'Destinations') {
    // Force empty href for Collections and Destinations top-level items
    $href = '';
    echo "<!-- Forcing empty href for " . $item->title . " (ID: " . $item->id . ") -->\n";
}

// Special handling for Collections menu items in the first column
if ((isset($currentItem) && $currentItem->title === 'Collections') ||
    (isset($currentItem) && $currentItem->id === 605) ||
    (isset($item->parent_id) && $item->parent_id === 605)) {

    // Check if this is a first column item (has an adventure icon)
    if (isset($adventure_icons[$item->title])) {
        // Force empty href for Collections menu items in the first column
        $href = '';
        echo "<!-- Forcing empty href for Collections item: " . $item->title . " (ID: " . $item->id . ") -->\n";
    }
}

// Check if this is an About Us menu item - using multiple detection methods for reliability
if ((isset($item->parent_id) && $item->parent_id === 362) ||
    (isset($currentItem) && $currentItem->id === 362) ||
    (isset($item->parent_title) && $item->parent_title === 'About Us') ||
    (isset($currentItem) && $currentItem->title === 'About Us')) {
    $isAboutUsMenuItem = true;

    // Generate URLs dynamically based on menu item properties
    $href = '';

    // Special case for "Refer and Earn - EverTrekker Rewards"
    if ($item->title === 'Refer and Earn - EverTrekker Rewards') {
        $href = '/evertrekker-rewards';
    }
    // For URL type menu items, use the link directly
    else if ($item->type === 'url') {
        // Make sure it's a full URL or starts with a slash
        if (strpos($item->link, 'http') === 0 || strpos($item->link, '/') === 0) {
            $href = $item->link;
        } else {
            // Add a leading slash if missing
            $href = '/' . $item->link;
        }
    }
    // For component type menu items (SP Page Builder)
    else if ($item->type === 'component' && strpos($item->link, 'com_sppagebuilder') !== false) {
        // Parse the query parameters
        parse_str(parse_url($item->link, PHP_URL_QUERY), $query);
        if (isset($query['id'])) {
            // Special case for the main About Us menu item (ID 362)
            if ($item->id === 362 || $item->title === 'About Us') {
                $href = '/about-us';
            } else {
                // Generate a URL based on the alias for sub-menu items
                $href = '/about-us/' . $item->alias;
            }
        }
    }
    // For alias type menu items
    else if ($item->type === 'alias') {
        if ($item->title === 'Blog') {
            $href = '/blog';
        } else if ($item->title === 'Podcast') {
            $href = '/podcast';
        } else if ($item->title === 'About Us') {
            $href = '/about-us';
        }
    }

    // Fallback: If no URL was generated, create one from the title and alias
    if (empty($href)) {
        // Special case for the main About Us menu item (ID 362)
        if ($item->id === 362 || $item->title === 'About Us') {
            $href = '/about-us';
        } else if (!empty($item->alias)) {
            $href = '/about-us/' . $item->alias;
        } else {
            // Create a URL-safe version of the title
            $safeTitle = strtolower(str_replace(' ', '-', preg_replace('/[^a-zA-Z0-9\s]/', '', $item->title)));
            $href = '/about-us/' . $safeTitle;
        }
    }

    // Debug the forced URL
    echo "<!-- Forced URL for About Us menu item: " . $href . " -->\n";
}

// Check if this is a non-clickable menu item (empty href or a Collections first column item)
if (empty($href) || $href === '' ||
    (isset($item->anchor_css) && strpos($item->anchor_css, 'zen-menu__sublink--collection') !== false)) {
    // Force empty href for Collections first column items
    if (isset($item->anchor_css) && strpos($item->anchor_css, 'zen-menu__sublink--collection') !== false) {
        $href = '';
        echo "<!-- Forcing empty href for Collections first column item: " . $item->title . " (ID: " . $item->id . ") -->\n";
    }
    // Add debug output
    echo "<!-- Using span for non-clickable item: " . $item->title . " (ID: " . $item->id . ") -->\n";
    // Use a span tag for non-clickable items
    ?><span <?php echo $class; ?> <?php echo $title; ?>><?php echo $linktype . $chevron_down; ?></span><?php
} else {
    // Add debug output
    echo "<!-- Using anchor for clickable item: " . $item->title . " (ID: " . $item->id . ") with href: " . $href . " -->\n";
    // Use an anchor tag for clickable items
    switch ($item->browserNav) {
        default:
        case 0:
            ?><a <?php echo $class; ?> href="<?php echo $href; ?>" <?php echo $title; ?>><?php echo $linktype . $chevron_down; ?></a><?php
            break;
        case 1:
            // _blank
            ?><a <?php echo $class; ?> href="<?php echo $href; ?>" target="_blank" <?php echo $title; ?>><?php echo $linktype . $chevron_down; ?></a><?php
            break;
        case 2:
            // Use JavaScript "window.open"
            $options = 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes';
            ?><a <?php echo $class; ?> href="<?php echo $href; ?>" onclick="window.open(this.href,'targetWindow','<?php echo $options;?>');return false;" <?php echo $title; ?>><?php echo $linktype . $chevron_down; ?></a><?php
            break;
    }
}